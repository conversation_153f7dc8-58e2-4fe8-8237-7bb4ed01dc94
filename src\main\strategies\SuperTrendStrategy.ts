import { TradingStrategy } from './TradingStrategy'

/**
 * A helper class to calculate a single SuperTrend indicator value and trend.
 * This class maintains its own state for ATR and trend calculation.
 */
class SuperTrendIndicator {
  private atrPeriod: number
  private multiplier: number
  private atr: number | null = null
  private trueRangeHistory: number[] = []
  public trend: 'UP' | 'DOWN' | 'NONE' = 'NONE'
  public value: number | null = null
  private upperBand: number = 0
  private lowerBand: number = 0

  constructor(atrPeriod: number, multiplier: number) {
    this.atrPeriod = atrPeriod
    this.multiplier = multiplier
  }

  /**
   * Updates the indicator with a new candle.
   * @param candle The current candle.
   * @param prevCandle The previous candle.
   */
  public update(candle: Candle, prevCandle: Candle | null): void {
    if (!prevCandle) {
      this.trueRangeHistory.push(candle.high - candle.low)
      return
    }

    // Calculate True Range
    const trueRange = Math.max(
      candle.high - candle.low,
      Math.abs(candle.high - prevCandle.close),
      Math.abs(candle.low - prevCandle.close)
    )

    this.trueRangeHistory.push(trueRange)

    // Keep only the required number of periods
    if (this.trueRangeHistory.length > this.atrPeriod) {
      this.trueRangeHistory.shift()
    }

    // Calculate ATR (Average True Range)
    if (this.trueRangeHistory.length >= this.atrPeriod) {
      this.atr = this.trueRangeHistory.reduce((sum, tr) => sum + tr, 0) / this.atrPeriod
    }

    if (this.atr === null) return

    // Calculate and update SuperTrend value
    const basicUpperBand = (candle.high + candle.low) / 2 + this.multiplier * this.atr
    const basicLowerBand = (candle.high + candle.low) / 2 - this.multiplier * this.atr

    this.upperBand =
      basicUpperBand < this.upperBand || prevCandle.close > this.upperBand
        ? basicUpperBand
        : this.upperBand
    this.lowerBand =
      basicLowerBand > this.lowerBand || prevCandle.close < this.lowerBand
        ? basicLowerBand
        : this.lowerBand

    if (this.value === null) {
      // Initial state
      this.trend = 'UP'
      this.value = this.lowerBand
      return
    }

    if (this.value === this.upperBand && candle.close > this.upperBand) {
      this.trend = 'UP'
      this.value = this.lowerBand
    } else if (this.value === this.lowerBand && candle.close < this.lowerBand) {
      this.trend = 'DOWN'
      this.value = this.upperBand
    } else {
      this.value = this.trend === 'UP' ? this.lowerBand : this.upperBand
    }
  }

  /**
   * Gets the current trend strength (distance from price to SuperTrend line)
   */
  public getTrendStrength(currentPrice: number): number {
    if (this.value === null) return 0
    return Math.abs(currentPrice - this.value) / currentPrice
  }
}

/**
 * Implements a trading strategy based on multiple SuperTrend indicators.
 * A BUY signal is generated when the price is above all SuperTrend lines.
 * A SELL signal is generated when the price is below all SuperTrend lines.
 */
export class SuperTrendStrategy extends TradingStrategy {
  private indicators: SuperTrendIndicator[]
  private prevCandle: Candle | null = null

  constructor(config: StrategyConfig) {
    super(config)

    // Default SuperTrend configurations if not provided
    const defaultSuperTrends = [
      { atrPeriod: 10, multiplier: 3.0 },
      { atrPeriod: 14, multiplier: 2.0 },
      { atrPeriod: 21, multiplier: 1.5 }
    ]

    const superTrends = config.superTrends || defaultSuperTrends
    this.indicators = superTrends.map(
      (params) => new SuperTrendIndicator(params.atrPeriod, params.multiplier)
    )
  }

  getName(): string {
    return 'SuperTrend Strategy'
  }

  getDescription(): string {
    return 'Multi-timeframe SuperTrend strategy that generates signals when all SuperTrend indicators align in the same direction.'
  }

  getCandleCount(): number {
    return this.priceHistory.length
  }

  async evaluate(candle: Candle): Promise<TradingDecision> {
    this.addCandle(candle)

    if (this.priceHistory.length < 2) {
      return {
        shouldTrade: false,
        reason: 'Not enough data to evaluate',
        confidence: 0
      }
    }

    // Sort the price history by time
    this.priceHistory.sort((a, b) => a.time - b.time)
    const currentCandle = this.priceHistory[this.priceHistory.length - 1]

    // Update all indicators
    this.indicators.forEach((indicator) => indicator.update(currentCandle, this.prevCandle))
    this.prevCandle = currentCandle

    const allTrends = this.indicators.map((i) => i.trend)

    // Check if we have enough data from all indicators
    if (allTrends.includes('NONE')) {
      return {
        shouldTrade: false,
        reason: 'SuperTrend indicators still initializing',
        confidence: 0
      }
    }

    // Determine signal based on trend alignment
    const isBuySignal = allTrends.every((t) => t === 'UP')
    const isSellSignal = allTrends.every((t) => t === 'DOWN')

    if (!isBuySignal && !isSellSignal) {
      return {
        shouldTrade: false,
        reason: 'SuperTrend indicators not aligned',
        confidence: 0
      }
    }

    // Calculate confidence based on trend strength and consistency
    const trendStrengths = this.indicators.map((indicator) =>
      indicator.getTrendStrength(currentCandle.close)
    )
    const avgTrendStrength =
      trendStrengths.reduce((sum, strength) => sum + strength, 0) / trendStrengths.length

    // Higher trend strength = higher confidence
    const baseConfidence = Math.min(avgTrendStrength * 100, 0.9) // Cap at 90%
    const minConfidence = this.config.minConfidence || 0.5

    if (baseConfidence < minConfidence) {
      return {
        shouldTrade: false,
        reason: `Confidence ${(baseConfidence * 100).toFixed(1)}% below minimum ${(minConfidence * 100).toFixed(1)}%`,
        confidence: baseConfidence
      }
    }

    // Calculate expiry based on configuration
    let expirySeconds = this.config.expiryToSeconds
    if (!expirySeconds && this.config.candlePeriodSeconds) {
      // Default to 2-3 candle periods for SuperTrend signals
      expirySeconds = this.config.candlePeriodSeconds * 2
    }

    const direction = isBuySignal ? 'high' : 'low'
    const action = isBuySignal ? 'BUY' : 'SELL'
    const reason = `All SuperTrend indicators show ${isBuySignal ? 'UP' : 'DOWN'} trend (${this.indicators.length} indicators aligned)`

    return {
      shouldTrade: true,
      direction,
      confidence: baseConfidence,
      reason,
      expirySeconds,
      metadata: {
        trendStrengths,
        avgTrendStrength,
        indicatorCount: this.indicators.length,
        action
      }
    }
  }
}
