import { ThresholdStrategy } from '../strategies/ThresholdStrategy'
import { SuperTrendStrategy } from '../strategies/SuperTrendStrategy'
import { TradingStrategy } from '../strategies/TradingStrategy'
// import { Logger } from '../../shared/utils/Logger'

// const logger = Logger.getContextLogger('SIGNAL')

export class SignalEngine {
  private tradeSettings: TradeSettings = {
    confidenceThreshold: 0.1, // Temporarily lowered for testing
    profitMargin: 0.005,
    riskTolerance: 0.002,
    leverage: 1,
    stopLoss: 0.05,
    takeProfit: 0.1
  }

  private strategies: Strategy[] = [{ name: 'threshold' }]
  private currentSignal: Signal | null = null
  private candleCount: number = 0
  private strategyInstances: Record<string, TradingStrategy> = {}

  constructor(strategies: Strategy[], tradeSettings: TradeSettings) {
    this.strategies = strategies
    this.tradeSettings = {
      ...this.tradeSettings,
      ...tradeSettings,
      // Ensure confidenceThreshold is always set to our default if not provided
      confidenceThreshold:
        tradeSettings.confidenceThreshold ?? this.tradeSettings.confidenceThreshold
    }
  }

  private strategyFactory = {
    threshold: () =>
      new ThresholdStrategy({
        threshold: this.tradeSettings.riskTolerance,
        minConfidence: this.tradeSettings.confidenceThreshold,
        candlePeriodSeconds: this.tradeSettings.candlePeriodSeconds,

        // ATR lookback of 14 bars
        atrPeriod: 14,

        // Scale ATR% up so that even modest wicks trigger
        // e.g. ATR ≈ 0.0003 (0.0007%) × 10 → 0.007% floor
        // Suggested Starting Range: Start with values between 0.2 and 0.5.
        // atrMultiplier: 0.5 means "trade if the move is at least 50% of the ATR."
        // atrMultiplier: 0.1 means "trade if the move is at least 10% of the ATR."
        atrMultiplier: 0.1,

        // Always require at least a 0.00005% spike, even if ATR×multiplier is lower
        minThresholdPct: 0.0000005,

        volatilityFilter: true,
        momentumConfirmation: true,
        consistencyCheck: true,
        expiryToSeconds: this.tradeSettings.expirySeconds
      }),
    supertrend: () =>
      new SuperTrendStrategy({
        minConfidence: this.tradeSettings.confidenceThreshold,
        candlePeriodSeconds: this.tradeSettings.candlePeriodSeconds,
        expiryToSeconds: this.tradeSettings.expirySeconds,
        superTrends: [
          { atrPeriod: 10, multiplier: 3.0 },
          { atrPeriod: 14, multiplier: 2.0 },
          { atrPeriod: 21, multiplier: 1.5 }
        ]
      })
  }

  async generate(candle: Candle): Promise<Signal[]> {
    const strategies = this.getActiveStrategies()
    const results = await Promise.all(
      strategies.map((strategy) => this.runStrategy(strategy, candle))
    )
    const signals = results.filter((s): s is Signal => s !== null)

    const finalSignal = this.combineSignalsByVoting(signals)

    const confThreshold = this.tradeSettings.confidenceThreshold ?? 0.4
    if (finalSignal.confidence < confThreshold) {
      finalSignal.action = 'HOLD'
      finalSignal.reason = `Confidence ${Math.round(finalSignal.confidence * 100)}% is below minimum ${confThreshold * 100}%`
      // logger.debug(
      //   `Signal blocked: confidence ${(finalSignal.confidence * 100).toFixed(1)}% < threshold ${(confThreshold * 100).toFixed(1)}%`
      // )
    }

    this.currentSignal = finalSignal
    return signals
  }

  getBestSignal(): Signal {
    return (
      this.currentSignal ?? {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    )
  }

  getCandleCount(): number {
    return this.candleCount
  }

  private getActiveStrategies(): Strategy[] {
    return this.strategies ?? []
  }

  private async runStrategy(strategy: Strategy, candle: Candle): Promise<Signal | null> {
    const factory = this.strategyFactory[strategy.name.toLowerCase()]
    if (!factory) return null

    const strategyInstance = this.getStrategyInstance(strategy.name.toLowerCase())
    const decision = await strategyInstance.evaluate(candle)

    this.candleCount = strategyInstance.getCandleCount()

    let action: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    if (decision.direction === 'high') action = 'BUY'
    else if (decision.direction === 'low') action = 'SELL'

    return {
      shouldTrade: decision.shouldTrade,
      action,
      reason: decision.reason ?? '',
      confidence: decision.confidence ?? 0,
      expirySeconds: decision.expirySeconds
    }
  }

  private combineSignals(signals: Signal[]): Signal {
    // 1. Handle the case where no signals are provided.
    if (!signals.length) {
      return {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    }

    // 2. Filter for actionable signals ('BUY' or 'SELL').
    const tradingSignals = signals.filter(
      (signal) => signal.action === 'BUY' || signal.action === 'SELL'
    )

    // 3. If there are any actionable signals, find the one with the highest confidence among them.
    if (tradingSignals.length > 0) {
      return tradingSignals.reduce((best, current) => {
        return current.confidence > best.confidence ? current : best
      })
    }

    // 4. If no actionable signals were found, return the highest-confidence 'HOLD' signal.
    return signals.reduce((best, current) => {
      return current.confidence > best.confidence ? current : best
    })
  }

  private combineSignalsByVoting(signals: Signal[]): Signal {
    // 1. Handle the case where no signals are provided.
    if (!signals.length) {
      return {
        shouldTrade: false,
        action: 'HOLD',
        reason: 'No signal generated',
        confidence: 0
      }
    }

    // 2. Count the votes for each action.
    const voteCounts: Record<'BUY' | 'SELL' | 'HOLD', number> = {
      BUY: 0,
      SELL: 0,
      HOLD: 0
    }

    signals.forEach((signal) => {
      voteCounts[signal.action]++
    })

    // 3. Determine the winning action by majority vote.
    let winningAction: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    let maxVotes = 0

    // Find the action with the most votes
    Object.entries(voteCounts).forEach(([action, count]) => {
      if (count > maxVotes) {
        maxVotes = count
        winningAction = action as 'BUY' | 'SELL' | 'HOLD'
      }
    })

    // 4. Handle ties by using confidence levels
    const actionsWithMaxVotes = Object.entries(voteCounts)
      .filter(([, count]) => count === maxVotes)
      .map(([action]) => action as 'BUY' | 'SELL' | 'HOLD')

    if (actionsWithMaxVotes.length > 1) {
      // Tie-breaking: find the action with highest confidence among tied actions
      let bestAction = actionsWithMaxVotes[0]
      let bestConfidence = 0

      actionsWithMaxVotes.forEach((action) => {
        const actionSignals = signals.filter((signal) => signal.action === action)
        const avgConfidence =
          actionSignals.reduce((sum, signal) => sum + signal.confidence, 0) / actionSignals.length

        if (avgConfidence > bestConfidence) {
          bestConfidence = avgConfidence
          bestAction = action
        }
      })

      winningAction = bestAction
    }

    // 5. Get all signals for the winning action
    const winningSignals = signals.filter((signal) => signal.action === winningAction)

    // 6. Calculate combined confidence and create result
    const avgConfidence =
      winningSignals.reduce((sum, signal) => sum + signal.confidence, 0) / winningSignals.length

    // Find the signal with highest confidence for the reason
    const bestSignal = winningSignals.reduce((best, current) =>
      current.confidence > best.confidence ? current : best
    )

    return {
      shouldTrade: winningAction !== 'HOLD',
      action: winningAction,
      reason: `${winningAction} signal won by ${maxVotes}/${signals.length} votes. ${bestSignal.reason}`,
      confidence: avgConfidence
    }
  }

  private getStrategyInstance(name: string): TradingStrategy {
    if (!this.strategyInstances[name]) {
      this.strategyInstances[name] = this.strategyFactory[name]()
    }

    return this.strategyInstances[name]
  }
}
