// Re-export types from shared types for renderer use
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface SessionResult {
  profitable: boolean
  totalProfit: number
  totalTrades: number
  winCount: number
  lossCount: number
  successRate: number
  strategy: string
  endReason: 'target_reached' | 'capital_depleted' | 'manual_stop'
  startTime: Date
  endTime: Date
  initialCapital: number
  finalCapital: number
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface MoneyManagementStatus {
  isSessionActive: boolean
  currentCapital: number
  currentProfit: number
  targetProfit: number
  initialCapital: number
  nextTradeAmount: number
  totalTrades: number
  winCount: number
  lossCount: number
  successRate: number
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface TradeSettings {
  tradeCapital?: number
  targetProfit?: number
  tradeAmount?: number
  expiry?: string
  strategy?: 'flat' | 'martingale' | 'fibonacci'
  confidenceThreshold?: number
  profitMargin?: number
  riskTolerance?: number
  leverage?: number
  stopLoss?: number
  takeProfit?: number
  expirySeconds?: number
  candlePeriodSeconds?: number
  customRules?: {
    name: string
    params: Record<string, unknown>
  }[]
}

// Make types globally available
declare global {
  interface Window {
    api: {
      send: (channel: string, data?: unknown) => void
      invoke: (channel: string, data?: unknown) => Promise<unknown>
      on: (channel: string, callback: (...args: unknown[]) => void) => () => void
    }
  }
}

export {}
