import { ThresholdStrategy } from '../strategies/ThresholdStrategy'
import { SuperTrendStrategy } from '../strategies/SuperTrendStrategy'
import { TradingStrategy } from '../strategies/TradingStrategy'

export class StrategyFactory {
  static createStrategy(type: StrategyType, config: StrategyConfig): TradingStrategy {
    switch (type) {
      case 'threshold':
        return new ThresholdStrategy(config)
      case 'supertrend':
        return new SuperTrendStrategy(config)
      // Add more cases for other strategies
      default:
        throw new Error(`Strategy ${type} not found`)
    }
  }

  static getAvailableStrategies(): Array<{
    type: StrategyType
    name: string
    description: string
  }> {
    return [
      {
        type: 'threshold',
        name: 'Threshold Strategy',
        description:
          'Advanced threshold strategy with momentum analysis, volatility filtering, and trend consistency checks.'
      },
      {
        type: 'supertrend',
        name: 'SuperTrend Strategy',
        description:
          'Multi-timeframe SuperTrend strategy that generates signals when all SuperTrend indicators align in the same direction.'
      }
    ]
  }

  static getDefaultConfig(type: StrategyType): StrategyConfig {
    switch (type) {
      case 'threshold':
        return {
          threshold: 0.02,
          minConfidence: 0.4,
          volatilityFilter: true,
          momentumConfirmation: true,
          consistencyCheck: true
        }
      case 'supertrend':
        return {
          minConfidence: 0.6,
          superTrends: [
            { atrPeriod: 10, multiplier: 3.0 },
            { atrPeriod: 14, multiplier: 2.0 },
            { atrPeriod: 21, multiplier: 1.5 }
          ]
        }
      // Add more cases for other strategies
      default:
        throw new Error(`Strategy ${type} not found`)
    }
  }
}
