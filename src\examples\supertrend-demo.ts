/**
 * SuperTrend Strategy Demo
 *
 * This file demonstrates how to use the SuperTrend strategy implementation.
 * It shows how to create the strategy, configure it, and evaluate trading decisions.
 */

import { SuperTrendStrategy } from '../main/strategies/SuperTrendStrategy'
import { StrategyFactory } from '../main/core/StrategyFactory'

// Example usage of SuperTrend strategy
async function demonstrateSuperTrendStrategy() {
  console.log('=== SuperTrend Strategy Demo ===\n')

  // 1. Create strategy using factory with default configuration
  console.log('1. Creating SuperTrend strategy with default configuration...')
  const defaultConfig = StrategyFactory.getDefaultConfig('supertrend')
  const strategy = StrategyFactory.createStrategy('supertrend', defaultConfig)

  console.log(`Strategy Name: ${strategy.getName()}`)
  console.log(`Description: ${strategy.getDescription()}`)
  console.log(`Default Config:`, JSON.stringify(defaultConfig, null, 2))
  console.log()

  // 2. Create sample candle data (simulating a trending market)
  console.log('2. Generating sample candle data...')
  const candles: Candle[] = []
  let basePrice = 100

  // Generate 25 candles with an upward trend
  for (let i = 0; i < 25; i++) {
    const time = Date.now() + i * 60000 // 1 minute intervals
    const open = basePrice
    const trendMove = 0.2 + Math.random() * 0.3 // Consistent upward movement
    const close = open + trendMove
    const high = Math.max(open, close) + Math.random() * 0.1
    const low = Math.min(open, close) - Math.random() * 0.1

    candles.push({ time, open, high, low, close })
    basePrice = close
  }

  console.log(`Generated ${candles.length} candles with upward trend`)
  console.log(
    `Price range: ${candles[0].open.toFixed(2)} -> ${candles[candles.length - 1].close.toFixed(2)}`
  )
  console.log()

  // 3. Evaluate each candle and show the strategy's decisions
  console.log('3. Evaluating trading decisions...')
  console.log('Candle | Price  | Decision | Confidence | Reason')
  console.log('-------|--------|----------|------------|--------')

  for (let i = 0; i < candles.length; i++) {
    const candle = candles[i]
    const decision = await strategy.evaluate(candle)

    const candleNum = (i + 1).toString().padStart(2, ' ')
    const price = candle.close.toFixed(2).padStart(6, ' ')
    const shouldTrade = decision.shouldTrade ? 'YES' : 'NO'
    const direction = decision.direction || 'HOLD'
    const confidence = ((decision.confidence || 0) * 100).toFixed(1) + '%'
    const reason = decision.reason || 'No reason'

    console.log(
      `${candleNum}     | ${price} | ${shouldTrade.padEnd(3)} ${direction.padEnd(4)} | ${confidence.padStart(6)} | ${reason}`
    )

    // Show detailed info for trading signals
    if (decision.shouldTrade) {
      console.log(`       └─ SIGNAL: ${direction} trade with ${confidence} confidence`)
      if (decision.expirySeconds) {
        console.log(`          Expiry: ${decision.expirySeconds} seconds`)
      }
      if (decision.metadata) {
        console.log(`          Metadata:`, decision.metadata)
      }
    }
  }

  console.log()
  console.log(`Final candle count: ${strategy.getCandleCount()}`)
  console.log()

  // 4. Demonstrate custom configuration
  console.log('4. Creating strategy with custom configuration...')
  const customConfig = {
    minConfidence: 0.7,
    expiryToSeconds: 180, // 3 minutes
    superTrends: [
      { atrPeriod: 7, multiplier: 2.5 }, // Fast, sensitive
      { atrPeriod: 14, multiplier: 2.0 }, // Medium
      { atrPeriod: 28, multiplier: 1.5 } // Slow, stable
    ]
  }

  const customStrategy = new SuperTrendStrategy(customConfig)
  console.log(`Custom Strategy: ${customStrategy.getName()}`)
  console.log(`Custom Config:`, JSON.stringify(customConfig, null, 2))
  console.log()

  // 5. Show available strategies
  console.log('5. Available strategies in the system:')
  const availableStrategies = StrategyFactory.getAvailableStrategies()
  availableStrategies.forEach((strategy) => {
    console.log(`- ${strategy.name} (${strategy.type}): ${strategy.description}`)
  })
}

// Run the demo if this file is executed directly
if (require.main === module) {
  demonstrateSuperTrendStrategy().catch(console.error)
}

export { demonstrateSuperTrendStrategy }
