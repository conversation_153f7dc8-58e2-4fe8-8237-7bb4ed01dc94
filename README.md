# bot-1

An Electron application with React and TypeScript

## Money Management System

This trading bot includes a comprehensive money management system with the following features:

### Core Features

- **Capital Protection**: Automatically stops the bot when capital is depleted to 10% of the original amount
- **Target Profit**: Automatically stops the bot when the target profit is reached
- **Balance Validation**: Prevents bot startup if trade capital exceeds account balance
- **Real-time Tracking**: Live updates of capital, profit, and trading statistics

### Loss Recovery Strategies

1. **Flat Strategy**: Uses the same trade amount for every trade
2. **Martingale Strategy**: Doubles the trade amount after each loss to recover losses quickly
3. **Fibonacci Strategy**: Uses Fibonacci sequence to gradually increase trade amounts after losses

### Session Management

- **Session Results Modal**: Displays comprehensive statistics when trading session ends
- **Real-time Status**: Shows current capital, profit progress, success rate, and next trade amount
- **Automatic Session End**: Triggers when target profit reached or capital protection activated

### UI Components

- **Trade Settings Form**: Configure capital, target profit, trade amount, expiry, and strategy
- **Money Management Status Panel**: Real-time display of session progress and statistics
- **Session Results Modal**: End-of-session summary with profit/loss analysis

## Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## Project Setup

### Install

```bash
$ npm install
```

### Development

```bash
$ npm run dev
```

### Build

```bash
# For windows
$ npm run build:win

# For macOS
$ npm run build:mac

# For Linux
$ npm run build:linux
```
