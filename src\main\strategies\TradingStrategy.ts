export abstract class TradingStrategy {
  protected config: StrategyConfig
  protected priceHistory: Candle[] = []

  constructor(config: StrategyConfig) {
    this.config = config
  }

  abstract getName(): string
  abstract getDescription(): string
  abstract evaluate(candle: Candle): Promise<TradingDecision>
  abstract getCandleCount(): number

  updateConfig(config: StrategyConfig): void {
    this.config = config
  }

  addCandle(candle: Candle): void {
    const exist = this.priceHistory.some((c) => c.time === candle.time)

    if (!exist) {
      this.priceHistory.push(candle)

      if (this.priceHistory.length > 200) {
        this.priceHistory = this.priceHistory.slice(-200)
      }
    }
  }

  addCandles(candles: Candle[]): void {
    // Create a Set of existing times for O(1) lookups
    const existingTimes = new Set(this.priceHistory.map((c) => c.time))

    // Filter out duplicates in one pass
    const newCandles = candles.filter((candle) => !existingTimes.has(candle.time))

    if (newCandles.length === 0) return

    // Add all new candles at once
    this.priceHistory.push(...newCandles)

    // Trim to size limit once at the end
    if (this.priceHistory.length > 200) {
      this.priceHistory = this.priceHistory.slice(-200)
    }
  }

  /**
   * Calculate the RSI for the given period with Wilder's smoothing method
   * @param period The period to calculate the RSI for
   * @returns RSIResult The RSI result containing the RSI value and trend
   */
  protected calculateRSI(period: number): RSIResult {
    const closes = this.priceHistory.map((price) => price.close)

    const priceChanges: number[] = []
    for (let i = 1; i < closes.length; i++) {
      const change = closes[i] - closes[i - 1]
      priceChanges.push(change)
    }

    const gains: number[] = priceChanges.map((change) => (change > 0 ? change : 0))
    const losses: number[] = priceChanges.map((change) => (change < 0 ? Math.abs(change) : 0))

    let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period
    let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period

    // Calculate subsequent average gains and losses using Wilder's smoothing method
    for (let i = period; i < gains.length; i++) {
      avgGain = (avgGain * (period - 1) + gains[i]) / period
      avgLoss = (avgLoss * (period - 1) + losses[i]) / period
    }

    if (avgLoss === 0) {
      return {
        rsi: 100,
        trend: 'overbought'
      }
    }

    const rs = avgGain / avgLoss
    const rsi = 100 - 100 / (1 + rs)

    let trend: 'overbought' | 'oversold' | 'neutral' = 'neutral'
    if (rsi > 70) {
      trend = 'overbought'
    } else if (rsi < 30) {
      trend = 'oversold'
    }

    return {
      rsi,
      trend
    }
  }

  // Helper function to add to your strategy or a utility file
  protected calculateSMA(period: number): number | null {
    if (this.priceHistory.length < period) {
      return null
    }
    const relevantHistory = this.priceHistory.slice(-period)
    const sum = relevantHistory.reduce((acc, candle) => acc + candle.close, 0)
    return sum / period
  }

  protected getStandardDeviation(values: number[]): number {
    const mean = values.reduce((a, b) => a + b, 0) / values.length
    const squareDiffs = values.map((value) => {
      const diff = value - mean
      return diff * diff
    })
    const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / values.length
    return Math.sqrt(avgSquareDiff)
  }
}
