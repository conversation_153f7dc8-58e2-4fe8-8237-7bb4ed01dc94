import { useState, useEffect } from 'react'
import SessionResultsModal from './SessionResultsModal'

interface TradeFormData {
  tradeCapital: number
  targetProfit: number
  tradeAmount: number
  expiry: string
  strategy: 'flat' | 'martingale' | 'fibon<PERSON>ci'
}

const TradeForm: React.FC = () => {
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [formData, setFormData] = useState<TradeFormData>({
    tradeCapital: 500,
    targetProfit: 100,
    tradeAmount: 50,
    expiry: 'S30',
    strategy: 'martingale'
  })

  const [isStarted, setIsStarted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showSessionResults, setShowSessionResults] = useState(false)
  const [sessionResult, setSessionResult] = useState<SessionResult | null>(null)

  const expiryOptions = [
    ['S5', 'S15', 'S30'],
    ['M1', 'M3', 'M5'],
    ['M30', 'H1', 'H4']
  ]

  useEffect(() => {
    // Listen for session end events
    const unsubscribe = window.api.on('broker:event', (...args: unknown[]) => {
      const [event, data] = args

      if (event === 'money-management:session-ended') {
        const result = data as SessionResult
        setSessionResult(result)
        setShowSessionResults(true)
        setIsStarted(false) // Bot is stopped when session ends
      } else if (event === 'bot:stopped') {
        setIsStarted(false)
      } else if (event === 'bot:started') {
        setIsStarted(true)
      }
    })

    return unsubscribe
  }, [])

  const handleInputChange = (field: keyof TradeFormData, value: number | string): void => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }))
  }

  const handleExpirySelect = (expiry: string): void => {
    setFormData((prev) => ({
      ...prev,
      expiry
    }))
  }

  const handleSubmitSettings = async (): Promise<void> => {
    try {
      window.api.send('trade:setSettings', {
        tradeCapital: formData.tradeCapital,
        targetProfit: formData.targetProfit,
        tradeAmount: formData.tradeAmount,
        expiry: formData.expiry,
        strategy: formData.strategy
      } as TradeSettings)
    } catch (err) {
      console.error('Failed to update trade settings:', err)
      setErrorMessage('Failed to update trade settings. Please try again.')
    }
  }

  const handleBotToggle = async (): Promise<void> => {
    setIsLoading(true)
    setErrorMessage(null)

    try {
      if (isStarted) {
        // Stop bot logic
        await window.api.invoke('bot:stop')
        setIsStarted(false)
      } else {
        // Validate inputs before starting
        if (!formData.tradeCapital || formData.tradeCapital <= 0) {
          throw new Error('Trade capital must be greater than 0')
        }
        if (!formData.targetProfit || formData.targetProfit <= 0) {
          throw new Error('Target profit must be greater than 0')
        }
        if (!formData.tradeAmount || formData.tradeAmount <= 0) {
          throw new Error('Trade amount must be greater than 0')
        }

        // Update settings first, then start bot
        await handleSubmitSettings()

        // Small delay to ensure settings are processed
        await new Promise((resolve) => setTimeout(resolve, 500))

        await window.api.invoke('bot:start')
        setIsStarted(true)
      }
    } catch (err) {
      console.error('Failed to toggle bot:', err)
      const errorMsg =
        err instanceof Error ? err.message : 'Failed to toggle the bot. Please try again.'
      setErrorMessage(errorMsg)
      setIsStarted(false)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-gray-800/80 p-4 rounded-sm shadow-lg w-full max-w-md">
      <h2 className="text-white text-xl font-bold mb-6 text-center">Trade Settings</h2>
      {errorMessage && (
        <div className="mb-4 text-red-500 text-center font-semibold bg-red-100/10 p-2 rounded">
          {errorMessage}
        </div>
      )}
      <div className="flex flex-col gap-2">
        {/* Trade Capital */}
        <div className="flex flex-col mb-2 w-full">
          <label className="block text-gray-300 text-sm font-medium mb-2">Trade Capital ($)</label>
          <input
            type="number"
            value={formData.tradeCapital}
            onChange={(e) => handleInputChange('tradeCapital', parseInt(e.target.value) || 0)}
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-transparent"
            placeholder="Enter trade capital"
            min="0"
          />
        </div>

        {/* Target Profit */}
        <div className="flex flex-col mb-2 w-full">
          <label className="block text-gray-300 text-sm font-medium mb-2">Target Profit ($)</label>
          <input
            type="number"
            value={formData.targetProfit}
            onChange={(e) => handleInputChange('targetProfit', parseInt(e.target.value) || 0)}
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-transparent"
            placeholder="Enter target profit"
            min="0"
          />
        </div>

        {/* Trade Amount */}
        <div className="flex flex-col mb-2 w-full">
          <label className="block text-gray-300 text-sm font-medium mb-2">Trade Amount ($)</label>
          <input
            type="number"
            value={formData.tradeAmount}
            onChange={(e) => handleInputChange('tradeAmount', parseInt(e.target.value) || 0)}
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-transparent"
            placeholder="Enter trade amount"
            min="0"
          />
        </div>

        {/* Trade Expiry */}
        <div className="flex flex-col mb-2 w-full">
          <label className="block text-gray-300 text-sm font-medium mb-3">Trade Expiry</label>
          <div className="flex flex-col gap-1 w-full">
            {expiryOptions.map((row, rowIndex) => (
              <div key={rowIndex} className="flex gap-1 w-full justify-between">
                {row.map((expiry) => (
                  <button
                    key={expiry}
                    onClick={() => handleExpirySelect(expiry)}
                    className={`flex-1 px-2 py-1 rounded-xs text-sm font-extrabold transition-colors ${
                      formData.expiry === expiry
                        ? 'bg-blue-600 text-white border-blue-500'
                        : 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600'
                    } border`}
                  >
                    {expiry}
                  </button>
                ))}
              </div>
            ))}
          </div>
        </div>

        {/* Money Management Strategy */}
        <div className="flex flex-col mb-2 w-full">
          <label className="block text-gray-300 text-sm font-medium mb-2">
            Money Management Strategy
          </label>
          <select
            value={formData.strategy}
            onChange={(e) =>
              handleInputChange('strategy', e.target.value as 'flat' | 'martingale' | 'fibonacci')
            }
            className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded-sm text-white focus:outline-none focus:ring-1 focus:ring-green-500/50 focus:border-transparent"
          >
            <option value="flat">Flat - Same amount every trade</option>
            <option value="martingale">Martingale - Double after loss</option>
            <option value="fibonacci">Fibonacci - Fibonacci sequence after loss</option>
          </select>
          <div className="text-xs text-gray-400 mt-1">
            {formData.strategy === 'flat' && 'Uses the same trade amount for every trade'}
            {formData.strategy === 'martingale' &&
              'Doubles the trade amount after each loss to recover losses quickly'}
            {formData.strategy === 'fibonacci' &&
              'Uses Fibonacci sequence to gradually increase trade amounts after losses'}
          </div>
        </div>

        {/* Start/Stop Bot Button */}
        <button
          onClick={handleBotToggle}
          disabled={isLoading}
          className={`w-full px-2 py-1 rounded-sm font-bold text-white transition-colors ${
            isStarted ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isLoading ? 'Loading...' : isStarted ? 'Stop Bot' : 'Start Bot'}
        </button>
      </div>

      {/* Session Results Modal */}
      <SessionResultsModal
        isOpen={showSessionResults}
        onClose={() => setShowSessionResults(false)}
        sessionResult={sessionResult}
      />
    </div>
  )
}

export default TradeForm
