interface Asset {
  /** 0 → Unique numerical identifier of the asset */
  id: number
  /** 1 → Trading symbol, e.g. `#AAPL` */
  symbol: string
  /** 2 → Human-readable name */
  name: string
  /** 3 → Asset class/category (`currency`, `stock`, `index`, …) */
  category: string
  /** 4 → Internal group identifier used by the platform */
  group: number
  /** 5 → Current payout / profit percentage offered (e.g. 80 ⇒ 80 %) */
  profit: number
  /** 6 → Minimum trade duration that can be selected (seconds) */
  minTime: number
  /** 7 → Maximum trade duration that can be selected (seconds) */
  maxTime: number
  /** 8 → Price precision (number of digits after the decimal separator) */
  precision: number
  /** 9 → 1 if the symbol is OTC, 0 otherwise */
  isOTC: number
  /** 10 → Identifier of the OTC twin (if regular market asset) */
  otcId: number
  /** 11 → Identifier of the regular-market twin (if OTC asset) */
  regularId: number
  /** 12 → Array with detailed trading schedule information */
  schedule: unknown[]
  /** 13 → Timestamp when the current schedule starts (Unix seconds) */
  scheduleStart: number
  /** 14 → Whether the asset is currently active/tradable */
  isActive: boolean
  /** 15 → List of timeframes the asset supports on the chart */
  timeframes: unknown[]
  /** 16 → Timestamp when the current schedule ends (Unix seconds) */
  scheduleEnd: number
  /** 17 → Minimum amount allowed per trade */
  minAmount: number
  /** 18 → Maximum amount allowed per trade */
  maxAmount: number
}
