{"name": "bot-1", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "bun run typecheck:node && bun run typecheck:web", "start": "TZ=UTC electron-vite preview", "dev": "TZ=UTC electron-vite dev", "build": "bun run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "bun run build && electron-builder --dir", "build:win": "bun run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "autoprefixer": "^10.4.21", "picocolors": "^1.1.1", "postcss": "^8.5.6", "socket.io-client": "^4.8.1"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.14.1", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.3.4", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^6.2.6"}}