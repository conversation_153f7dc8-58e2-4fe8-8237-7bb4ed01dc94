import { useState, useEffect } from 'react'

type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'authenticated'

export function useConnectionStatus(): ConnectionState {
  const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected')

  useEffect(() => {
    const unsubscribe = window.api.on('broker:event', (...args: unknown[]) => {
      const [event, payload] = args

      if (event === 'stateChanged') {
        // The payload should be the new state
        setConnectionState(payload as ConnectionState)
      }
    })

    // Initial state check
    window.api.send('broker:getState')

    return () => unsubscribe()
  }, [])

  return connectionState
}
