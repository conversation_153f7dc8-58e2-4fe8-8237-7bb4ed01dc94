interface StrategyConfig {
  cooldownPeriod?: number
  minConfidence?: number
  expiryToSeconds?: number // User-defined expiry
  candlePeriodSeconds?: number // Chart period

  // ATR-scaled threshold
  atrPeriod?: number // default 14
  atrMultiplier?: number // e.g. 1.0–8.0
  minThresholdPct?: number // floor threshold, e.g. 0.002 for 0.2%
  expirySeconds?: number // optional override for expiry

  // Threshold Strategy
  threshold?: number
  maxVolatilityPercent?: number
  volatilityFilter?: boolean
  momentumConfirmation?: boolean
  consistencyCheck?: boolean

  // SuperTrend Strategy
  superTrends?: { atrPeriod: number; multiplier: number }[]
}

interface PriceData {
  current: number
  previous: number
  timestamp: number
  trend: 'up' | 'down' | 'neutral'
  change: number
  changePercent: number
}

interface TradingDecision {
  shouldTrade: boolean
  direction?: 'high' | 'low'
  confidence?: number // 0-1 scale
  reason?: string
  metadata?: unknown // Additional strategy-specific data
  expirySeconds?: number
}

interface RSIResult {
  rsi: number
  trend: 'overbought' | 'oversold' | 'neutral'
}
