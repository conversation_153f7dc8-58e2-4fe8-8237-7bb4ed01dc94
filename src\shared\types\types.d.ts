type StrategyType =
  | 'threshold'
  | 'moving-average'
  | 'rsi'
  | 'macd'
  | 'bollinger-bands'
  | 'oscillate'
  | 'slide'
  | 'ai'
  | 'custom'
  | 'supertrend'

type BrokerState =
  | 'connected'
  | 'disconnected'
  | 'authenticated'
  | 'connecting'
  | 'disconnecting'
  | 'authenticating'

type RawAssetTuple = [
  number, // id
  string, // symbol
  string, // name
  string, // category
  number, // group
  number, // profit
  number, // minTime
  number, // maxTime
  number, // precision
  number, // isOTC
  number, // otcId
  number, // regularId
  unknown[], // schedule
  number, // scheduleStart
  number, // isActive
  unknown[], // timeframes
  number, // scheduleEnd
  number, // minAmount
  number // maxAmount
]

type StreamData = [assetSymbol: string, timestamp: number, price: number]
