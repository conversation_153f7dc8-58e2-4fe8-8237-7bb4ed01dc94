import TradesTable from './components/TradesTable'
import TradeForm from './components/TradeForm'
import { ConnectionStatus } from './components/ConnectionStatus'
import MoneyManagementStatus from './components/MoneyManagementStatus'

function App(): React.JSX.Element {
  return (
    <main className="flex flex-col relative items-center justify-center">
      <div className="flex gap-2 justify-start w-full p-2">
        <TradesTable />

        <div className="flex flex-col gap-2">
          <TradeForm />
          <MoneyManagementStatus />
        </div>
      </div>
      <ConnectionStatus />
    </main>
  )
}

export default App
