import { useEffect, useState } from 'react'
import { getCooldownSeconds } from '../../../shared/utils/formatters'

interface TradeData {
  id: string
  status: 'WIN' | 'LOSS' | 'PROGRESS'
  closeInSeconds: number
  direction: 'BUY' | 'SELL'
  payout: number
  amount: number
  profit: number
}

const TradesTable: React.FC = () => {
  const [balance, setBalance] = useState<number>(0)
  const [trades, setTrades] = useState<TradeData[]>([])

  const calculateWinLossRatio = (): string => {
    const finishedTrades = trades.filter((t) => t.status !== 'PROGRESS')
    const wins = finishedTrades.filter((t) => t.status === 'WIN').length
    const total = finishedTrades.length
    return total > 0 ? `${Math.round((wins / total) * 100)}%` : '0%'
  }

  useEffect(() => {
    const unsub = window.api.on('broker:event', (...args: unknown[]) => {
      const [event, data] = args
      if (event === 'balance:updated') {
        const balanceData = data as Balance
        setBalance(balanceData.balance)
      }

      if (event === 'trade:opened') {
        const tradeData = data as OpenOrderResponse
        setTrades((prevTrades) => [
          ...prevTrades,
          {
            id: tradeData.id,
            status: 'PROGRESS',
            closeInSeconds: getCooldownSeconds(tradeData.openTimestamp, tradeData.closeTimestamp),
            direction: tradeData.command === 1 ? 'SELL' : 'BUY',
            payout: tradeData.percentProfit,
            amount: tradeData.amount,
            profit: 0
          }
        ])
      }

      if (event === 'trade:closed') {
        const tradeData = data as CloseOrderResponse

        // Update the trade status and profit for all closed deals
        setTrades((prevTrades) => {
          return prevTrades.map((trade) => {
            // Check if this trade matches any of the closed deals
            const matchingDeal = tradeData.deals.find((deal) => deal.id === trade.id)

            if (matchingDeal) {
              return {
                ...trade,
                status: matchingDeal.profit > 0 ? 'WIN' : 'LOSS',
                closeInSeconds: 0,
                profit: matchingDeal.profit
              }
            }
            return trade
          })
        })
      }
    })

    const unsubSession = window.api.on('trade:newSession', () => {
      setTrades([])
    })

    return () => {
      unsub()
      unsubSession()
    }
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setTrades((prevTrades) =>
        prevTrades.map((trade) => ({
          ...trade,
          closeInSeconds:
            trade.status === 'PROGRESS' && trade.closeInSeconds > 0
              ? trade.closeInSeconds - 1
              : trade.closeInSeconds
        }))
      )
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  const getProfitColor = (profit: number): string => {
    if (profit > 0) return 'text-green-400'
    if (profit < 0) return 'text-red-500'
    return 'text-gray-500'
  }

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'WIN':
        return 'bg-green-800 text-white'
      case 'LOSS':
        return 'bg-red-800 text-white'
      case 'PROGRESS':
        return 'bg-blue-800 text-white'
      default:
        return 'bg-gray-800 text-white'
    }
  }

  return (
    <div className="w-full bg-gray-800/80 p-2 rounded-sm shadow-md">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Trades: <span className="text-white !font-semibold">{trades.length}</span>
        </div>

        <div className="text-sm text-gray-500">W/L: {calculateWinLossRatio()}</div>
        <div className="text-sm text-gray-500">
          Balance: <span className="text-green-400 !font-semibold">${balance}</span>
        </div>
      </div>
      <div className="overflow-y-auto max-h-90 custom-scrollbar">
        <table className="w-full border-collapse bg-gray-800 text-white overflow-hidden">
          <thead>
            <tr className="bg-gray-700">
              <th className="px-2 py-1 text-left font-bold text-xs text-gray-200">#</th>
              <th className="px-2 py-1 text-left font-bold text-xs text-gray-200">STATUS</th>
              <th className="px-2 py-1 text-left font-bold text-xs text-gray-200">CLOSE</th>
              <th className="px-2 py-1 text-left font-bold text-xs text-gray-200">DIR</th>
              <th className="px-2 py-1 text-left font-bold text-xs text-gray-200">PAY</th>
              <th className="px-2 py-1 text-left font-bold text-xs text-gray-200">AMT</th>
              <th className="px-2 py-1 text-left font-bold text-xs text-gray-200">P/L</th>
            </tr>
          </thead>
          <tbody>
            {trades.map((trade, index) => (
              <tr key={trade.id} className="border-b border-gray-600">
                <td className="px-2 py-2 text-xs">{index + 1}</td>
                <td className="px-2 py-2 text-xs">
                  <span
                    className={`px-2 py-1 rounded text-[11px] w-full text-center block ${getStatusBadgeClass(trade.status)}`}
                  >
                    {trade.status}
                  </span>
                </td>
                <td className="px-2 py-2 text-xs text-center">
                  {trade.status === 'PROGRESS' ? `${trade.closeInSeconds}s` : '-'}
                </td>
                <td className="px-2 py-2 text-xs text-center">
                  <span className={trade.direction === 'BUY' ? 'text-green-400' : 'text-red-500'}>
                    {trade.direction}
                  </span>
                </td>
                <td className="px-2 py-2 text-xs text-right">{trade.payout}%</td>
                <td className="px-2 py-2 text-xs text-right">${trade.amount}</td>
                <td className={`px-2 py-2 text-xs text-right ${getProfitColor(trade.profit)}`}>
                  ${trade.profit}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default TradesTable
