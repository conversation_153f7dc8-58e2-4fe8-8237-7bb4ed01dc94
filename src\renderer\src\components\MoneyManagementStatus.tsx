import { useState, useEffect } from 'react'

interface MoneyManagementStatusProps {
  className?: string
}

const MoneyManagementStatus: React.FC<MoneyManagementStatusProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<MoneyManagementStatus | null>(null)
  const [accountBalance, setAccountBalance] = useState<number>(0)

  useEffect(() => {
    // Fetch initial status
    const fetchStatus = async (): Promise<void> => {
      try {
        const [mmStatus, balance] = await Promise.all([
          window.api.invoke('money-management:getStatus'),
          window.api.invoke('money-management:getAccountBalance')
        ])
        setStatus(mmStatus as MoneyManagementStatus | null)
        setAccountBalance(balance as number)
      } catch (error) {
        console.error('Failed to fetch money management status:', error)
      }
    }

    fetchStatus()

    // Listen for status updates
    const unsubscribe = window.api.on('broker:event', (...args: unknown[]) => {
      const [event, data] = args

      if (event === 'money-management:status') {
        console.log(`Money management status updated: ${JSON.stringify(data)}`)
        setStatus(data as MoneyManagementStatus)
      } else if (event === 'balance:updated') {
        console.log(`Account balance updated: ${JSON.stringify(data)}`)
        const balanceData = data as { balance: number }
        setAccountBalance(balanceData.balance)
      }
    })

    return unsubscribe
  }, [])

  if (!status || !status.isSessionActive) {
    return (
      <div className={`bg-gray-800/80 p-4 rounded-sm shadow-lg ${className}`}>
        <h3 className="text-white text-lg font-bold mb-2">Money Management</h3>
        <div className="text-gray-400 text-center py-4">No active trading session</div>
        <div className="text-sm text-gray-300">
          <div className="flex justify-between">
            <span>Account Balance:</span>
            <span className="font-bold">${accountBalance.toFixed(2)}</span>
          </div>
        </div>
      </div>
    )
  }

  const profitPercentage =
    status.initialCapital > 0 ? (status.currentProfit / status.initialCapital) * 100 : 0
  const capitalPercentage =
    status.initialCapital > 0 ? (status.currentCapital / status.initialCapital) * 100 : 0
  const targetPercentage =
    status.targetProfit > 0 ? (status.currentProfit / status.targetProfit) * 100 : 0

  const getProgressColor = (percentage: number, isProfit = false): string => {
    if (isProfit) {
      if (percentage >= 100) return 'bg-green-500'
      if (percentage >= 50) return 'bg-green-400'
      if (percentage >= 0) return 'bg-yellow-400'
      return 'bg-red-400'
    }
    if (percentage >= 80) return 'bg-green-500'
    if (percentage >= 50) return 'bg-yellow-400'
    if (percentage >= 20) return 'bg-orange-400'
    return 'bg-red-500'
  }

  return (
    <div className={`bg-gray-800/80 p-4 rounded-sm shadow-lg ${className}`}>
      <h3 className="text-white text-lg font-bold mb-4">Money Management</h3>

      {/* Current Profit/Loss */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-300 text-sm">Current Profit/Loss</span>
          <span
            className={`font-bold ${status.currentProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}
          >
            ${status.currentProfit >= 0 ? '+' : ''}${status.currentProfit.toFixed(2)}
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(Math.abs(profitPercentage), true)}`}
            style={{ width: `${Math.min(Math.abs(profitPercentage), 100)}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-400 mt-1">
          {profitPercentage >= 0 ? '+' : ''}
          {profitPercentage.toFixed(1)}% of initial capital
        </div>
      </div>

      {/* Target Progress */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-300 text-sm">Target Progress</span>
          <span className="text-white font-bold">
            ${status.currentProfit.toFixed(2)} / ${status.targetProfit.toFixed(2)}
          </span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="h-2 bg-blue-500 rounded-full transition-all duration-300"
            style={{ width: `${Math.min(Math.max(targetPercentage, 0), 100)}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-400 mt-1">
          {targetPercentage.toFixed(1)}% of target reached
        </div>
      </div>

      {/* Remaining Capital */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-300 text-sm">Remaining Capital</span>
          <span className="text-white font-bold">${status.currentCapital.toFixed(2)}</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(capitalPercentage)}`}
            style={{ width: `${Math.max(capitalPercentage, 0)}%` }}
          ></div>
        </div>
        <div className="text-xs text-gray-400 mt-1">
          {capitalPercentage.toFixed(1)}% of initial capital
        </div>
      </div>

      {/* Trading Statistics */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        <div className="bg-gray-700 p-2 rounded text-center">
          <div className="text-xs text-gray-400">Total Trades</div>
          <div className="font-bold text-white">{status.totalTrades}</div>
        </div>
        <div className="bg-gray-700 p-2 rounded text-center">
          <div className="text-xs text-gray-400">Success Rate</div>
          <div
            className={`font-bold ${status.successRate >= 60 ? 'text-green-400' : status.successRate >= 40 ? 'text-yellow-400' : 'text-red-400'}`}
          >
            {status.successRate.toFixed(1)}%
          </div>
        </div>
        <div className="bg-gray-700 p-2 rounded text-center">
          <div className="text-xs text-gray-400">Wins</div>
          <div className="font-bold text-green-400">{status.winCount}</div>
        </div>
        <div className="bg-gray-700 p-2 rounded text-center">
          <div className="text-xs text-gray-400">Losses</div>
          <div className="font-bold text-red-400">{status.lossCount}</div>
        </div>
      </div>

      {/* Next Trade Amount */}
      <div className="bg-gray-700 p-3 rounded">
        <div className="flex justify-between items-center">
          <span className="text-gray-300 text-sm">Next Trade Amount</span>
          <span className="font-bold text-blue-400">${status.nextTradeAmount.toFixed(2)}</span>
        </div>
      </div>
    </div>
  )
}

export default MoneyManagementStatus
