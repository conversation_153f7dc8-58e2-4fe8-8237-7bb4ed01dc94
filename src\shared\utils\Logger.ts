import pc from 'picocolors'
import fs from 'fs'
import path from 'path'
import { formatTime } from './formatters'

type LogLevel = 'info' | 'success' | 'warn' | 'error' | 'debug' | 'trade' | 'performance'

const EMOJIS: Record<LogLevel, string> = {
  info: 'ℹ️ ',
  success: '✅',
  warn: '⚠️ ',
  error: '❌',
  debug: '🐞',
  trade: '📈',
  performance: '⏱️ '
}

const COLORS = {
  info: pc.cyan,
  success: pc.green,
  warn: pc.yellow,
  error: pc.red,
  debug: pc.magenta,
  trade: pc.blue,
  performance: pc.gray,
  timestamp: pc.dim,
  prefix: pc.bold
}

interface LoggerOptions {
  prefix?: string
  showLogLevel?: boolean
  showTimestamp?: boolean
  logFile?: string
}

export interface ILogger {
  info(message: string, ...args: unknown[]): void
  success(message: string, ...args: unknown[]): void
  warn(message: string, ...args: unknown[]): void
  error(message: string, ...args: unknown[]): void
  debug(message: string, ...args: unknown[]): void
  trade(message: string, ...args: unknown[]): void
  performance(message: string, ...args: unknown[]): void
  log(message: string, ...args: unknown[]): void
}

export class Logger implements ILogger {
  private static instance: Logger | null = null
  private config: Required<LoggerOptions>
  private logsDir: string = './logs'

  private constructor(config: LoggerOptions) {
    this.config = {
      prefix: config.prefix || 'APP',
      showLogLevel: config.showLogLevel ?? true,
      showTimestamp: config.showTimestamp ?? true,
      logFile: config.logFile || ''
    }
    this.ensureLogsDirectory()
  }

  public static getInstance(config?: LoggerOptions): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config || {})
    }
    return Logger.instance
  }

  /**
   * Creates a context-specific logger with a custom prefix
   */
  public static getContextLogger(prefix: string): ILogger {
    const instance = Logger.getInstance()
    return new ContextLogger(instance, prefix)
  }

  private ensureLogsDirectory(): void {
    try {
      if (!fs.existsSync(this.logsDir)) {
        fs.mkdirSync(this.logsDir, { recursive: true })
      }
    } catch (error) {
      console.error('Failed to create logs directory:', error)
    }
  }

  private getCurrentLogFile(): string {
    const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
    return path.join(this.logsDir, `${today}.log`)
  }

  private formatTimestamp(): string {
    return formatTime(new Date())
  }

  private formatMessage(level: LogLevel, message: string, contextPrefix?: string): string {
    const emoji = EMOJIS[level]
    const parts: string[] = []

    if (this.config.showTimestamp) {
      parts.push(`${this.formatTimestamp()}`)
    }

    if (this.config.showLogLevel) {
      if (level === 'info' || level === 'warn') {
        parts.push(`${emoji} [${level.toUpperCase()}] `)
      } else {
        parts.push(`${emoji} [${level.toUpperCase()}]`)
      }
    }

    const prefix = contextPrefix || this.config.prefix
    if (prefix) {
      parts.push(`[${prefix}]`)
    }

    parts.push(message)

    return parts.join(' ')
  }

  private formatForTerminal(level: LogLevel, message: string, contextPrefix?: string): string {
    const color = COLORS[level]
    const formattedMessage = this.formatMessage(level, message, contextPrefix)

    let coloredMessage = `${color(formattedMessage)}`

    if (this.config.showTimestamp) {
      const timestampMatch = formattedMessage.match(/^\[([^\]]+)\]/)
      if (timestampMatch) {
        coloredMessage = coloredMessage.replace(
          timestampMatch[0],
          COLORS.timestamp(timestampMatch[0])
        )
      }
    }

    const prefix = contextPrefix || this.config.prefix
    if (prefix) {
      const prefixPattern = `[${prefix}]`
      coloredMessage = coloredMessage.replace(prefixPattern, COLORS.prefix(prefixPattern))
    }

    return coloredMessage
  }

  private writeToFile(level: LogLevel, message: string, contextPrefix?: string): void {
    try {
      const logFile = this.config.logFile || this.getCurrentLogFile()
      const formattedMessage = this.formatMessage(level, message, contextPrefix)
      const logEntry = `${formattedMessage}\n`

      fs.appendFileSync(logFile, logEntry, 'utf8')
    } catch (error) {
      console.error('Failed to write to log file:', error)
    }
  }

  public logMessage(
    level: LogLevel,
    message: string,
    contextPrefix?: string,
    ...args: unknown[]
  ): void {
    // Console output with colors
    console.log(this.formatForTerminal(level, message, contextPrefix), ...args)

    // File output
    this.writeToFile(level, message, contextPrefix)
  }

  public info(message: string, ...args: unknown[]): void {
    this.logMessage('info', message, undefined, ...args)
  }

  public success(message: string, ...args: unknown[]): void {
    this.logMessage('success', message, undefined, ...args)
  }

  public warn(message: string, ...args: unknown[]): void {
    this.logMessage('warn', message, undefined, ...args)
  }

  public error(message: string, ...args: unknown[]): void {
    this.logMessage('error', message, undefined, ...args)
  }

  public debug(message: string, ...args: unknown[]): void {
    this.logMessage('debug', message, undefined, ...args)
  }

  public trade(message: string, ...args: unknown[]): void {
    this.logMessage('trade', message, undefined, ...args)
  }

  public performance(message: string, ...args: unknown[]): void {
    this.logMessage('performance', message, undefined, ...args)
  }

  public setLogFile(logFile: string): void {
    this.config.logFile = logFile

    const dir = path.dirname(logFile)
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }
    } catch (error) {
      console.error('Failed to create log file directory:', error)
    }
  }

  public log(message: string, ...args: unknown[]): void {
    this.info(message, ...args)
  }

  public static async cleanup(): Promise<void> {
    if (Logger.instance) {
      // No explicit cleanup needed for file operations as they're synchronous
      // But we can null the instance to prevent further usage
      Logger.instance = null
    }
  }
}

/**
 * Context-specific logger that delegates to the main logger instance
 * but uses its own prefix for logging messages
 */
class ContextLogger implements ILogger {
  constructor(
    private mainLogger: Logger,
    private contextPrefix: string
  ) {}

  public info(message: string, ...args: unknown[]): void {
    this.mainLogger.logMessage('info', message, this.contextPrefix, ...args)
  }

  public success(message: string, ...args: unknown[]): void {
    this.mainLogger.logMessage('success', message, this.contextPrefix, ...args)
  }

  public warn(message: string, ...args: unknown[]): void {
    this.mainLogger.logMessage('warn', message, this.contextPrefix, ...args)
  }

  public error(message: string, ...args: unknown[]): void {
    this.mainLogger.logMessage('error', message, this.contextPrefix, ...args)
  }

  public debug(message: string, ...args: unknown[]): void {
    this.mainLogger.logMessage('debug', message, this.contextPrefix, ...args)
  }

  public trade(message: string, ...args: unknown[]): void {
    this.mainLogger.logMessage('trade', message, this.contextPrefix, ...args)
  }

  public performance(message: string, ...args: unknown[]): void {
    this.mainLogger.logMessage('performance', message, this.contextPrefix, ...args)
  }

  public log(message: string, ...args: unknown[]): void {
    this.info(message, ...args)
  }
}
