import { useConnectionStatus } from '../hooks/useConnectionStatus'

export function ConnectionStatus(): React.JSX.Element {
  const connectionState = useConnectionStatus()

  const getStatusColor = (): string => {
    switch (connectionState) {
      case 'authenticated':
        return 'bg-green-500'
      case 'connected':
        return 'bg-blue-500'
      case 'connecting':
        return 'bg-yellow-500'
      case 'disconnected':
      default:
        return 'bg-red-500'
    }
  }

  const getStatusText = (): string => {
    switch (connectionState) {
      case 'authenticated':
        return 'Authenticated'
      case 'connected':
        return 'Connected'
      case 'connecting':
        return 'Connecting'
      case 'disconnected':
      default:
        return 'Disconnected'
    }
  }

  const getStatusIcon = (): string => {
    switch (connectionState) {
      case 'authenticated':
        return '✓'
      case 'connected':
        return '⟳'
      case 'connecting':
        return '⟲'
      case 'disconnected':
      default:
        return '✕'
    }
  }

  return (
    <div className="flex items-center space-x-2 px-3 py-1 rounded-full bg-gray-800 text-sm">
      <div className={`w-2 h-2 rounded-full ${getStatusColor()}`}></div>
      <span className="text-xs font-medium">{getStatusText()}</span>
      <span className="text-xs">{getStatusIcon()}</span>
    </div>
  )
}
